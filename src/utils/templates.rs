use actix_web::{HttpResponse, Result};
use actix_session::Session;
use tera::{Context, Tera};
use crate::utils::csrf::get_csrf_token;
use serde::Serialize;

lazy_static::lazy_static! {
    pub static ref TEMPLATES: Tera = {
        match Tera::new("templates/**/*.html") {
            Ok(mut tera) => {
                tera.autoescape_on(vec!["html"]);
                tera
            }
            Err(e) => {
                println!("Template parsing error: {}", e);
                let mut tera = Tera::default();
                tera.autoescape_on(vec!["html"]);
                tera
            }
        }
    };
}

#[derive(Serialize)]
pub struct UserContext {
    pub user_id: Option<i32>,
    pub username: Option<String>,
    pub role: Option<String>,
    pub is_authenticated: bool,
    pub is_admin: bool,
    pub is_superadmin: bool,
}

impl UserContext {
    pub fn from_session(session: &Session) -> Self {
        let user_id = session.get::<i32>("user_id").unwrap_or(None);
        let username = session.get::<String>("username").unwrap_or(None);
        let role = session.get::<String>("role").unwrap_or(None);
        let is_authenticated = user_id.is_some();

        let is_admin = role.as_ref().map_or(false, |r| r == "admin" || r == "superadmin");
        let is_superadmin = role.as_ref().map_or(false, |r| r == "superadmin");

        UserContext {
            user_id,
            username,
            role,
            is_authenticated,
            is_admin,
            is_superadmin,
        }
    }
}

/// Add user context to a template context
pub fn add_user_context(context: &mut Context, session: &Session) {
    let user_context = UserContext::from_session(session);

    // Add individual fields for backward compatibility
    if let Some(ref username) = user_context.username {
        context.insert("username", username);
    }
    if let Some(ref role) = user_context.role {
        context.insert("role", role);
    }

    // Add the full user object for template conditionals
    context.insert("user", &user_context);
    context.insert("user_context", &user_context);
    context.insert("is_authenticated", &user_context.is_authenticated);
}

pub fn render_template(template_name: &str, context: &Context) -> Result<HttpResponse> {
    let body = TEMPLATES.render(template_name, context).map_err(|e| {
        println!("Template error: {}", e);
        actix_web::error::ErrorInternalServerError(e)
    })?;
    Ok(HttpResponse::Ok().content_type("text/html").body(body))
}

/// Render a template with user context and CSRF token
///
/// # Arguments
/// * `template_name` - The name of the template to render
/// * `context` - The context to use for rendering
/// * `session` - The user's session
///
/// # Returns
/// * `Result<HttpResponse>` - The rendered template
pub fn render_template_with_context(template_name: &str, context: &mut Context, session: &Session) -> Result<HttpResponse> {
    // Add user context
    add_user_context(context, session);

    // Add CSRF token to context
    let csrf_token = get_csrf_token(session);
    context.insert("csrf_token", &csrf_token);

    render_template(template_name, context)
}

/// Render a template with CSRF token included in the context (legacy function)
///
/// # Arguments
/// * `template_name` - The name of the template to render
/// * `context` - The context to use for rendering
/// * `session` - The user's session
///
/// # Returns
/// * `Result<HttpResponse>` - The rendered template
pub fn render_template_with_csrf(template_name: &str, context: &mut Context, session: &Session) -> Result<HttpResponse> {
    render_template_with_context(template_name, context, session)
}
