use actix_web::{test, web, App};
use actix_session::{storage::CookieSessionStore, SessionMiddleware};
use actix_web::cookie::Key;
use diesel::r2d2::{self, ConnectionManager};
use diesel::{SqliteConnection, RunQueryDsl};
use garden_planner_web::{routes, DbPool};
use garden_planner_web::services::herba_gatherer::{HerbaGatherer, PlantIdentifier};
use garden_planner_web::services::season_planner::{SeasonPlanner, AutoPlanRequest, OptimizationGoals};
use serde_json::json;
use std::sync::Once;
use chrono::NaiveDate;

static INIT: Once = Once::new();

fn setup_test_db() -> DbPool {
    INIT.call_once(|| {
        std::env::set_var("DATABASE_URL", "sqlite://test_herba_gardening_app.db");
        std::env::set_var("RUST_LOG", "debug");
        env_logger::init();
    });

    let database_url = std::env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let manager = ConnectionManager::<SqliteConnection>::new(database_url);
    let pool = r2d2::Pool::builder()
        .build(manager)
        .expect("Failed to create pool.");

    // Clean up existing data for fresh tests
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");
    diesel::sql_query("DELETE FROM season_plan_plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM season_plans").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM herba_plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM plants").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM properties").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM seasons").execute(&mut conn).ok();
    diesel::sql_query("DELETE FROM users WHERE username != 'admin'").execute(&mut conn).ok();

    pool
}

// Helper function to create test app - removed complex type signature

#[actix_web::test]
async fn test_herba_gatherer_basic_functionality() {
    let gatherer = HerbaGatherer::new();

    let identifier = PlantIdentifier {
        latin_name: Some("Melissa officinalis".to_string()),
        common_name: Some("Lemon Balm".to_string()),
        partial_name: None,
        family: Some("Lamiaceae".to_string()),
        genus: Some("Melissa".to_string()),
    };

    // Test basic plant data creation (fallback when no internet)
    let basic_data = gatherer.create_basic_plant_data(&identifier);
    assert_eq!(basic_data.latin_name, "Melissa officinalis");
    assert_eq!(basic_data.common_name, "Lemon Balm");
    assert!(basic_data.water_requirements.is_some());
    assert!(basic_data.sun_requirements.is_some());
}

#[actix_web::test]
async fn test_herba_integration_with_plants() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test plants list access
    let req = test::TestRequest::get().uri("/plants/list").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status().is_redirection());
}

#[actix_web::test]
async fn test_season_planner_basic_functionality() {
    let pool = setup_test_db();
    let mut conn = pool.get().expect("Failed to get connection");

    let planner = SeasonPlanner::new();

    // Create test data
    let start_date = NaiveDate::from_ymd_opt(2024, 3, 1).unwrap();
    let end_date = NaiveDate::from_ymd_opt(2024, 10, 31).unwrap();

    let optimization_goals = OptimizationGoals {
        maximize_yield: true,
        maximize_diversity: false,
        minimize_water_usage: false,
        maximize_companion_planting: true,
        enable_succession_planting: true,
        prefer_perennials: false,
    };

    let auto_plan_request = AutoPlanRequest {
        property_id: 1,
        growing_area_id: None,
        season_id: 1,
        start_date,
        end_date,
        preferred_plants: vec![1, 2, 3], // Assuming these plants exist
        optimization_goals,
    };

    // Test that the planner can handle the request without crashing
    // Note: This might fail due to missing test data, but should not panic
    let result = planner.create_automatic_plan(&mut conn, auto_plan_request).await;
    // We expect this to either succeed or fail gracefully
    assert!(result.is_ok() || result.is_err());
}

#[actix_web::test]
async fn test_auto_plan_form_access() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test access to auto plan form
    let req = test::TestRequest::get().uri("/season_plans/auto_plan").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status().is_redirection());
}

#[actix_web::test]
async fn test_notification_system_integration() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test notifications list access
    let req = test::TestRequest::get().uri("/notifications/list").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status().is_redirection());
}

#[actix_web::test]
async fn test_property_visualization_access() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test property list access
    let req = test::TestRequest::get().uri("/property").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status().is_redirection());
}

#[actix_web::test]
async fn test_admin_dashboard_access() {
    let pool = setup_test_db();
    let secret_key = Key::generate();

    let app = test::init_service(
        App::new()
            .wrap(SessionMiddleware::new(
                CookieSessionStore::default(),
                secret_key.clone(),
            ))
            .app_data(web::Data::new(pool))
            .configure(routes::init)
    ).await;

    // Test admin dashboard access
    let req = test::TestRequest::get().uri("/admin").to_request();
    let resp = test::call_service(&app, req).await;
    assert!(resp.status().is_success() || resp.status().is_redirection());
}
