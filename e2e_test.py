#!/usr/bin/env python3
"""
E2E Test for Garden Planner Application

This script tests the complete user flow:
1. User registration
2. Household creation
3. Property creation
4. Drawing property shapes
5. Drawing growing areas
6. Creating a second user
7. Sharing household with second user
"""

import requests
import json
import time
import random
import string
from urllib.parse import urljoin

class GardenPlannerE2E:
    def __init__(self, base_url="http://127.0.0.1:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None

    def log(self, message):
        print(f"[E2E] {message}")

    def get_csrf_token(self, response_text):
        """Extract CSRF token from HTML response"""
        import re
        match = re.search(r'name="csrf_token" value="([^"]+)"', response_text)
        if match:
            return match.group(1)
        return None

    def test_homepage(self):
        """Test that homepage loads correctly"""
        self.log("Testing homepage...")
        try:
            response = self.session.get(self.base_url, timeout=10)
            self.log(f"Homepage response status: {response.status_code}")
            self.log(f"Homepage content length: {len(response.text)}")
            assert response.status_code == 200
            assert "Garden Planner" in response.text
            self.log("✓ Homepage loads correctly")
        except Exception as e:
            self.log(f"Homepage test failed: {e}")
            raise

    def test_user_registration(self, username, password):
        """Test user registration flow"""
        self.log(f"Testing user registration for {username}...")

        try:
            # Get registration form
            response = self.session.get(f"{self.base_url}/auth/register")
            self.log(f"Registration form status: {response.status_code}")
            assert response.status_code == 200

            # Extract CSRF token
            self.csrf_token = self.get_csrf_token(response.text)
            self.log(f"CSRF token: {self.csrf_token}")
            assert self.csrf_token is not None, "CSRF token not found"

            # Submit registration
            data = {
                'username': username,
                'password': password,
                'password_confirm': password,
                'csrf_token': self.csrf_token
            }

            response = self.session.post(f"{self.base_url}/auth/register", data=data)
            self.log(f"Registration response status: {response.status_code}")
            self.log(f"Registration response headers: {response.headers}")

            if response.status_code != 302:
                self.log(f"Registration response content: {response.text[:500]}")

            # Should redirect to wizard after successful registration
            assert response.status_code == 302
            location = response.headers.get('Location', '')
            self.log(f"Redirect location: {location}")
            assert "/wizard/household" in location or location == "/wizard/household"

            self.log(f"✓ User {username} registered successfully")
        except Exception as e:
            self.log(f"Registration test failed: {e}")
            raise

    def test_household_creation(self, household_name):
        """Test household creation in wizard"""
        self.log(f"Testing household creation: {household_name}...")

        # Get household creation form
        response = self.session.get(f"{self.base_url}/wizard/household")
        assert response.status_code == 200

        # Extract CSRF token
        self.csrf_token = self.get_csrf_token(response.text)

        # Submit household creation
        data = {
            'name': household_name,
            'csrf_token': self.csrf_token
        }

        response = self.session.post(f"{self.base_url}/wizard/household", data=data)
        assert response.status_code == 302
        assert "/wizard/invite" in response.headers.get('Location', '')

        self.log(f"✓ Household '{household_name}' created successfully")

    def test_invite_step(self):
        """Test invite members step (skip for now)"""
        self.log("Testing invite step...")

        # Get invite form
        response = self.session.get(f"{self.base_url}/wizard/invite")
        assert response.status_code == 200

        # Skip invites for now
        data = {
            'emails': '',
            'csrf_token': self.get_csrf_token(response.text)
        }

        response = self.session.post(f"{self.base_url}/wizard/invite", data=data)
        assert response.status_code == 302
        assert "/wizard/property" in response.headers.get('Location', '')

        self.log("✓ Invite step completed")

    def test_property_creation(self, property_name):
        """Test property creation in wizard"""
        self.log(f"Testing property creation: {property_name}...")

        # Get property creation form
        response = self.session.get(f"{self.base_url}/wizard/property")
        assert response.status_code == 200

        # Submit property creation
        data = {
            'name': property_name,
            'outside_area': '100.0',
            'inside_area': '50.0',
            'floors': '2',
            'csrf_token': self.get_csrf_token(response.text)
        }

        response = self.session.post(f"{self.base_url}/wizard/property", data=data)
        assert response.status_code == 302
        assert "/wizard/draw_property_shapes" in response.headers.get('Location', '')

        self.log(f"✓ Property '{property_name}' created successfully")

    def test_property_shapes_drawing(self):
        """Test property shapes drawing step"""
        self.log("Testing property shapes drawing...")

        # Get drawing form
        response = self.session.get(f"{self.base_url}/wizard/draw_property_shapes")
        assert response.status_code == 200
        assert "Draw Property" in response.text

        # Simulate drawing a simple rectangle
        shape_data = json.dumps([
            {"x": 10, "y": 10},
            {"x": 90, "y": 10},
            {"x": 90, "y": 90},
            {"x": 10, "y": 90}
        ])

        data = {
            'shape_data': shape_data,
            'shape_type': 'polygon',
            'area': '80.0',
            'floor_no': '-1',  # Outside
            'csrf_token': self.get_csrf_token(response.text)
        }

        response = self.session.post(f"{self.base_url}/wizard/save_property_shape", data=data)
        assert response.status_code == 302

        # Finish the floor
        response = self.session.post(f"{self.base_url}/wizard/finish_floor", data={'floor_no': '-1'})

        self.log("✓ Property shapes drawn successfully")

    def test_growing_areas_drawing(self):
        """Test growing areas drawing step"""
        self.log("Testing growing areas drawing...")

        # Get growing areas form
        response = self.session.get(f"{self.base_url}/wizard/draw_growing_areas")
        assert response.status_code == 200

        # Simulate drawing a growing area
        shape_data = json.dumps([
            {"x": 20, "y": 20},
            {"x": 80, "y": 20},
            {"x": 80, "y": 80},
            {"x": 20, "y": 80}
        ])

        data = {
            'shape_data': shape_data,
            'shape_type': 'polygon',
            'area': '36.0',
            'floor_no': '-1',
            'csrf_token': self.get_csrf_token(response.text)
        }

        response = self.session.post(f"{self.base_url}/wizard/save_growing_area_shape", data=data)
        assert response.status_code == 302

        self.log("✓ Growing areas drawn successfully")

    def test_wizard_completion(self):
        """Test wizard completion"""
        self.log("Testing wizard completion...")

        # Finish growing areas
        response = self.session.get(f"{self.base_url}/wizard/finish_grow_floor")

        # Should eventually redirect to finish
        response = self.session.get(f"{self.base_url}/wizard/finish_wizard")

        self.log("✓ Wizard completed successfully")

    def test_logout(self):
        """Test user logout"""
        self.log("Testing logout...")

        response = self.session.get(f"{self.base_url}/auth/logout")
        assert response.status_code == 302
        assert "/auth/login" in response.headers.get('Location', '')

        self.log("✓ User logged out successfully")

    def test_login(self, username, password):
        """Test user login"""
        self.log(f"Testing login for {username}...")

        # Get login form
        response = self.session.get(f"{self.base_url}/auth/login")
        assert response.status_code == 200

        # Submit login
        data = {
            'username': username,
            'password': password,
            'csrf_token': self.get_csrf_token(response.text)
        }

        response = self.session.post(f"{self.base_url}/auth/login", data=data)
        assert response.status_code == 302
        assert response.headers.get('Location') == '/'

        self.log(f"✓ User {username} logged in successfully")

    def run_complete_test(self):
        """Run the complete E2E test"""
        self.log("Starting complete E2E test...")

        # Generate random usernames to avoid conflicts
        user1 = f"testuser1_{random.randint(1000, 9999)}"
        user2 = f"testuser2_{random.randint(1000, 9999)}"
        password = "testpass123"
        household_name = f"Test Household {random.randint(100, 999)}"
        property_name = f"Test Property {random.randint(100, 999)}"

        try:
            # Test homepage
            self.test_homepage()

            # Test first user registration and setup
            self.test_user_registration(user1, password)
            self.test_household_creation(household_name)
            self.test_invite_step()
            self.test_property_creation(property_name)
            self.test_property_shapes_drawing()
            self.test_growing_areas_drawing()
            self.test_wizard_completion()

            # Test logout and second user
            self.test_logout()
            self.test_user_registration(user2, password)

            # Test login with first user
            self.test_logout()
            self.test_login(user1, password)

            self.log("🎉 All E2E tests passed successfully!")
            return True

        except Exception as e:
            self.log(f"❌ Test failed: {str(e)}")
            return False

if __name__ == "__main__":
    tester = GardenPlannerE2E()
    success = tester.run_complete_test()
    exit(0 if success else 1)
