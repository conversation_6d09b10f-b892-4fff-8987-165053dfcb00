{% extends "base.html" %}
{% block title %}Login{% endblock %}
{% block content %}
<div class="max-w-md mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow mt-10">
  <h2 class="text-2xl font-bold mb-4 text-center">Login to Gardening Planner</h2>
  {% if error %}
  <p class="text-red-600">{{ error }}</p>
  {% endif %}
  <form method="post" action="/auth/login" class="space-y-4">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    <div>
      <label for="username" class="block text-sm font-medium">Username</label>
      <input type="text" id="username" name="username" required class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
    </div>
    <div>
      <label for="password" class="block text-sm font-medium">Password</label>
      <input type="password" id="password" name="password" required class="w-full mt-1 p-2 border rounded dark:bg-gray-700 dark:border-gray-600">
    </div>
    <button type="submit" class="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700">Login</button>
  </form>
  <p class="mt-4 text-center">Don't have an account? <a href="/auth/register" class="text-green-600 hover:underline">Register here</a></p>
</div>
{% endblock %}
